import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { Helmet } from 'react-helmet-async';
import { Spin, Alert } from 'antd';
import { useLanguage } from '../context/LanguageContext';
import EnhancedOpportunityCard from '../components/EnhancedOpportunityCard';
import GreatYOPPagination from '../components/GreatYOPPagination';
import PageEndSuggestions from '../components/PageEndSuggestions';
import AdPlacement from '../components/AdPlacement';

interface Opportunity {
  id: number;
  title: string;
  description: string;
  type: 'internship' | 'training' | 'conference' | 'workshop' | 'competition';
  organization: string;
  location: string;
  isRemote: boolean;
  deadline: string;
  startDate?: string;
  endDate?: string;
  applicationLink?: string;
  thumbnail?: string;
  isActive: boolean;
  tags?: string[];
}

interface PaginationData {
  total: number;
  page: number;
  limit: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

const Opportunities: React.FC = () => {
  const { translations, direction } = useLanguage();
  const [opportunities, setOpportunities] = useState<Opportunity[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [pagination, setPagination] = useState<PaginationData>({
    total: 0,
    page: 1,
    limit: 6, // Show 6 opportunities per page (3x2 grid)
    totalPages: 0,
    hasNextPage: false,
    hasPreviousPage: false
  });

  // Fetch opportunities with pagination
  useEffect(() => {
    fetchOpportunities();
  }, [pagination.page]);

  const fetchOpportunities = async () => {
    try {
      setLoading(true);
      setError(null);

      // Build query parameters
      const params = new URLSearchParams();
      params.append('page', pagination.page.toString());
      params.append('limit', pagination.limit.toString());
      params.append('active', 'true');
      params.append('orderBy', 'deadline');
      params.append('orderDirection', 'ASC');

      const response = await fetch(`${process.env.REACT_APP_API_URL || 'http://localhost:5000'}/api/opportunities?${params.toString()}`);

      if (!response.ok) {
        throw new Error('Failed to fetch opportunities');
      }

      const data = await response.json();
      console.log('Opportunities API response:', data);

      // Handle the correct API response format: { success: true, data: [...], pagination: {...} }
      const opportunitiesData = data.data || data.opportunities || [];
      const paginationData = data.pagination || {};

      setOpportunities(opportunitiesData);
      setPagination(paginationData || {
        total: opportunitiesData.length || 0,
        page: 1,
        limit: 6,
        totalPages: Math.ceil((opportunitiesData.length || 0) / 6),
        hasNextPage: false,
        hasPreviousPage: false
      });
      setError(null);
    } catch (err) {
      console.error('Error fetching opportunities:', err);
      setError('Failed to load opportunities. Please try again later.');
      setOpportunities([]);
    } finally {
      setLoading(false);
    }
  };

  // Handle page change
  const handlePageChange = (page: number) => {
    setPagination(prev => ({
      ...prev,
      page
    }));
    // Scroll to top when page changes
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  // Handle opportunity card click
  const handleOpportunityClick = (id: number) => {
    window.location.href = `/opportunities/${id}`;
  };

  return (
    <>
      <Helmet>
        <title>Toutes les Opportunités Professionnelles | MaBourse</title>
        <meta name="description" content="Explorez notre collection complète d'opportunités professionnelles : stages, formations, conférences, ateliers et concours pour tous les niveaux." />
        <meta name="keywords" content="opportunités professionnelles, stages, formations, conférences, ateliers, concours, développement professionnel" />
      </Helmet>

      <div className="min-h-screen bg-gradient-to-br from-primary-50/30 via-white to-primary-100/20">
        {/* Compact Hero Section - Matching Specific Opportunity Pages */}
        <section className="bg-white pt-20 pb-4" style={{ paddingTop: '4rem' }}>
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex items-center mb-3">
              <span className="text-3xl mr-3">💼</span>
              <h1 style={{
                fontSize: '24px',
                marginBottom: '0',
                color: '#2563eb',
                fontWeight: '700',
                textTransform: 'capitalize'
              }}>
                Toutes les Opportunités Professionnelles Disponibles
              </h1>
            </div>

            <div className="archive-description">
              <p style={{
                marginBottom: '15px',
                textAlign: 'justify',
                color: '#3d3d3d',
                fontSize: '17px',
                lineHeight: '30px',
                fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", Arial, sans-serif'
              }}>
                Explorez notre collection complète d'opportunités professionnelles pour tous les niveaux et domaines.
                Ces opportunités exceptionnelles sont proposées par des entreprises prestigieuses, des universités de renommée mondiale, et des institutions dédiées à l'excellence professionnelle.
              </p>
              <p style={{
                marginBottom: '20px',
                textAlign: 'justify',
                color: '#3d3d3d',
                fontSize: '17px',
                lineHeight: '30px',
                fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", Arial, sans-serif'
              }}>
                Que vous visiez les stages professionnels, les formations spécialisées, les conférences internationales, les ateliers pratiques, ou les concours d'excellence, chaque opportunité offre un développement professionnel adapté à vos ambitions. Explorez, comparez et trouvez l'opportunité qui transformera votre parcours de carrière.
              </p>
            </div>

            {/* Quick Stats */}
            <div className="flex flex-wrap gap-3 mt-3">
              <div className="flex items-center bg-blue-50 px-3 py-1.5 rounded-lg">
                <svg className="w-4 h-4 text-blue-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0V6a2 2 0 012 2v6M8 8v6a2 2 0 002 2h4a2 2 0 002-2V8M8 8V6a2 2 0 012-2h4a2 2 0 012 2v2" />
                </svg>
                <span className="text-sm font-medium text-blue-800">Opportunités Vérifiées</span>
              </div>
              <div className="flex items-center bg-green-50 px-3 py-1.5 rounded-lg">
                <svg className="w-4 h-4 text-green-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                </svg>
                <span className="text-sm font-medium text-green-800">Développement Professionnel</span>
              </div>
              <div className="flex items-center bg-purple-50 px-3 py-1.5 rounded-lg">
                <svg className="w-4 h-4 text-purple-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <span className="text-sm font-medium text-purple-800">Toutes Spécialités</span>
              </div>
            </div>
          </div>
        </section>

        {/* Desktop Ad - Only visible on large screens */}
        <div className="hidden lg:block py-8 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <AdPlacement
              adSlot="1234567890"
              adSize="leaderboard"
              responsive={true}
            />
          </div>
        </div>



        {/* Content Section */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-16">
          {/* Main Content - Full Width */}
          <div className="w-full">

            {loading ? (
              <div className="flex justify-center items-center py-16">
                <Spin size="large" tip="Chargement des opportunités..." />
              </div>
            ) : error ? (
              <Alert
                message="Erreur"
                description={error}
                type="error"
                showIcon
                className="mb-6 rounded-xl shadow-md"
              />
            ) : (
              <>
                {/* Mobile Ad - Only visible on small screens */}
                <div className="mb-8 md:hidden">
                  <AdPlacement
                    adSlot="2345678901"
                    adSize="rectangle"
                    responsive={true}
                    fullWidth={true}
                  />
                </div>

                <div className="gy-pcard-wrap">
                  {opportunities.map((opportunity, index) => (
                    <EnhancedOpportunityCard
                      key={opportunity.id}
                      id={opportunity.id}
                      title={opportunity.title}
                      thumbnail={opportunity.thumbnail}
                      deadline={opportunity.deadline}
                      isActive={opportunity.isActive}
                      type={opportunity.type}
                      organization={opportunity.organization}
                      location={opportunity.location}
                      isRemote={opportunity.isRemote}
                      onClick={handleOpportunityClick}
                      index={index}
                      variant="greatyop"
                    />
                  ))}
                </div>

                {/* No Results Message */}
                {opportunities.length === 0 && (
                  <div className="text-center py-16 bg-gray-50 rounded-2xl shadow-sm border border-gray-100">
                    <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0V6a2 2 0 012 2v6M8 8v6a2 2 0 002 2h4a2 2 0 002-2V8M8 8V6a2 2 0 012-2h4a2 2 0 012 2v2" />
                    </svg>
                    <h3 className="mt-4 text-lg font-medium text-gray-900">Aucune opportunité trouvée</h3>
                    <p className="mt-2 text-sm text-gray-500 max-w-md mx-auto">
                      Essayez d'ajuster vos critères de recherche pour trouver ce que vous cherchez.
                    </p>
                  </div>
                )}

                {/* Pagination */}
                {pagination.total > 0 && (
                  <div style={{ marginBottom: '8px' }}>
                    <GreatYOPPagination
                      current={pagination.page}
                      total={pagination.total}
                      pageSize={pagination.limit}
                      onChange={handlePageChange}
                      showQuickJumper={false}
                    />
                  </div>
                )}

              </>
            )}
          </div>

          {/* Page End Suggestions - Directly after pagination with minimal gap */}
          <PageEndSuggestions
            currentPageType="opportunity"
            currentItem=""
          />
        </div>

        {/* Newsletter Section - Same as Country Pages */}
        <section className="relative py-8 overflow-hidden">
          {/* Background with gradient and pattern */}
          <div className="absolute inset-0 bg-gradient-to-br from-primary-dark to-primary">
            <div className="absolute inset-0 opacity-10">
              <svg width="100%" height="100%">
                <defs>
                  <pattern id="grid" width="80" height="80" patternUnits="userSpaceOnUse">
                    <path d="M 80 0 L 0 0 0 80" fill="none" stroke="white" strokeWidth="1" />
                  </pattern>
                </defs>
                <rect width="100%" height="100%" fill="url(#grid)" />
              </svg>
            </div>
          </div>
          {/* Decorative elements */}
          <div className="absolute top-0 right-0 -mt-20 -mr-20 w-80 h-80 rounded-full bg-white opacity-10 blur-3xl"></div>
          <div className="absolute bottom-0 left-0 -mb-20 -ml-20 w-80 h-80 rounded-full bg-white opacity-10 blur-3xl"></div>

          <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="bg-white rounded-xl shadow-lg overflow-hidden">
              <div className="grid grid-cols-1 lg:grid-cols-5">
                {/* Left column - Image */}
                <div className="relative hidden lg:block lg:col-span-2">
                  <img
                    src="/assets/newsletter-image.jpg"
                    alt="Professional opportunities"
                    className="absolute inset-0 h-full w-full object-cover"
                    onError={(e) => {
                      const target = e.target as HTMLImageElement;
                      target.src = 'https://images.unsplash.com/photo-1522202176988-66273c2fd55f?ixlib=rb-4.0.3&q=85&fm=jpg&crop=entropy&cs=srgb&w=800';
                    }}
                  />
                  <div className="absolute inset-0 bg-gradient-to-r from-primary-dark/80 to-transparent mix-blend-multiply"></div>
                  <div className="absolute inset-0 flex items-center justify-center p-6">
                    <div className="text-white">
                      <h3 className="text-lg font-bold mb-3" style={{ color: 'white' }}>Restez Informé</h3>
                      <ul className="space-y-2 text-sm">
                        {[
                          'Nouvelles opportunités disponibles',
                          'Dates limites importantes',
                          'Conseils exclusifs',
                          'Témoignages de professionnels'
                        ].map((benefit, index) => (
                          <li key={index} className="flex items-start">
                            <svg className="h-4 w-4 text-green-400 mr-2 mt-0.5 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                            </svg>
                            <span className="leading-tight" style={{ color: 'white' }}>{benefit}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>

                {/* Right column - Form */}
                <div className="p-6 lg:col-span-3">
                  <div className="flex items-center mb-4">
                    <div className="flex items-center justify-center w-10 h-10 rounded-full bg-primary/10 mr-3">
                      <svg className="w-5 h-5 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                      </svg>
                    </div>
                    <div>
                      <h2 className="text-xl font-bold text-gray-900">Newsletter Opportunités</h2>
                      <p className="text-sm text-gray-600">Développement professionnel et opportunités</p>
                    </div>
                  </div>

                  <p className="text-sm text-gray-600 mb-4 leading-relaxed">
                    Recevez les dernières opportunités professionnelles directement dans votre boîte mail.
                    Nous ne vous enverrons pas de spam.
                  </p>

                  <form className="space-y-4">
                    <div className="flex gap-3">
                      <input
                        type="email"
                        placeholder="<EMAIL>"
                        className="flex-1 px-4 py-3 text-sm rounded-lg border border-gray-300 focus:ring-primary focus:border-primary focus:outline-none focus:ring-2 transition-colors duration-200"
                      />
                      <button
                        type="submit"
                        className="px-6 py-3 bg-primary text-white text-sm font-medium rounded-lg hover:bg-primary-dark transition-colors duration-300 whitespace-nowrap"
                      >
                        S'abonner
                      </button>
                    </div>

                    <div className="flex items-start">
                      <input
                        id="privacy"
                        type="checkbox"
                        className="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded mt-0.5"
                      />
                      <label htmlFor="privacy" className="ml-3 block text-sm text-gray-600 leading-relaxed">
                        J'accepte de recevoir des emails concernant les opportunités professionnelles et le développement de carrière
                      </label>
                    </div>
                  </form>
                </div>
              </div>
            </div>
          </div>
        </section>
      </div>
    </>
  );
};

export default Opportunities;
