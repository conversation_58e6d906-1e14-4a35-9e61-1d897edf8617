import React, { useState, useEffect } from 'react';
import { Helmet } from 'react-helmet-async';

import EnhancedOpportunityCard from './EnhancedOpportunityCard';
import GreatYOPPagination from './GreatYOPPagination';
import UnifiedSidebar from './UnifiedSidebar';
import CommentsSection from './CommentsSection';
import AdPlacement from './AdPlacement';
import { Spin, Alert } from 'antd';

interface Opportunity {
  id: number;
  title: string;
  description: string;
  type: 'internship' | 'training' | 'conference' | 'workshop' | 'competition';
  organization: string;
  location: string;
  isRemote: boolean;
  deadline: string;
  startDate?: string;
  endDate?: string;
  applicationLink?: string;
  thumbnail?: string;
  isActive: boolean;
  tags?: string[];
}

interface StandardOpportunityPageConfig {
  type: string;
  title: string;
  description: string;
  keywords: string;
  heroTitle: string;
  heroDescription: string;
  infoTitle: string;
  infoContent: string;
  benefits: string[];
  apiEndpoint: string;
}

interface StandardOpportunityPageProps {
  config: StandardOpportunityPageConfig;
}

const StandardOpportunityPage: React.FC<StandardOpportunityPageProps> = ({ config }) => {
  const [opportunities, setOpportunities] = useState<Opportunity[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 6,
    total: 0
  });

  const handleOpportunityClick = (id: number) => {
    window.location.href = `/opportunities/${id}`;
  };

  const fetchOpportunities = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await fetch(`/api/opportunities/type/${encodeURIComponent(config.type)}?page=${pagination.page}&limit=${pagination.limit}`);
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const data = await response.json();
      
      if (data.success) {
        setOpportunities(data.data || []);
        setPagination(prev => ({
          ...prev,
          total: data.pagination?.total || data.data?.length || 0
        }));
      } else {
        throw new Error(data.message || 'Failed to fetch opportunities');
      }
    } catch (error) {
      console.error('Error fetching opportunities:', error);
      setError('Impossible de charger les opportunités. Veuillez réessayer plus tard.');
      setOpportunities([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchOpportunities();
  }, [pagination.page, config.type]); // eslint-disable-line react-hooks/exhaustive-deps

  const handlePageChange = (page: number) => {
    setPagination(prev => ({ ...prev, page }));
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const sidebarConfig = {
    type: 'opportunities' as const,
    currentItem: config.type,
    limit: 10
  };

  return (
    <>
      <Helmet>
        <title>{config.title}</title>
        <meta name="description" content={config.description} />
        <meta name="keywords" content={config.keywords} />
      </Helmet>

      <div className="min-h-screen bg-gradient-to-br from-primary-50/30 via-white to-primary-100/20">
        {/* Compact Hero Section - Matching Website Design */}
        <section className="bg-white pt-20 pb-4" style={{ paddingTop: '4rem' }}>
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex items-center mb-3">
              <span className="text-3xl mr-3">💼</span>
              <h1 style={{
                fontSize: '24px',
                marginBottom: '0',
                color: '#2563eb',
                fontWeight: '700',
                textTransform: 'capitalize'
              }}>
                {config.heroTitle}
              </h1>
            </div>

            <div className="archive-description">
              <p style={{
                marginBottom: '15px',
                textAlign: 'justify',
                color: '#3d3d3d',
                fontSize: '17px',
                lineHeight: '30px',
                fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", Arial, sans-serif'
              }}>
                {config.heroDescription}
              </p>
              <p style={{
                marginBottom: '20px',
                textAlign: 'justify',
                color: '#3d3d3d',
                fontSize: '17px',
                lineHeight: '30px',
                fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", Arial, sans-serif'
              }}>
                {config.infoContent}
              </p>
            </div>

            {/* Quick Stats */}
            <div className="flex flex-wrap gap-3 mt-3">
              <div className="flex items-center bg-blue-50 px-3 py-1.5 rounded-lg">
                <svg className="w-4 h-4 text-blue-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0V6a2 2 0 012 2v6M8 8v6a2 2 0 002 2h4a2 2 0 002-2V8M8 8V6a2 2 0 012-2h4a2 2 0 012 2v2" />
                </svg>
                <span className="text-sm font-medium text-blue-800">Opportunités Vérifiées</span>
              </div>
              <div className="flex items-center bg-green-50 px-3 py-1.5 rounded-lg">
                <svg className="w-4 h-4 text-green-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                </svg>
                <span className="text-sm font-medium text-green-800">Développement Professionnel</span>
              </div>
              <div className="flex items-center bg-purple-50 px-3 py-1.5 rounded-lg">
                <svg className="w-4 h-4 text-purple-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <span className="text-sm font-medium text-purple-800">Toutes Spécialités</span>
              </div>
            </div>
          </div>
        </section>

        {/* Desktop Ad - Only visible on large screens */}
        <div className="hidden lg:block py-8 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <AdPlacement
              adSlot="1234567890"
              adSize="leaderboard"
              responsive={true}
            />
          </div>
        </div>

        {/* Content Section */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-16">
          {/* Opportunities Grid - Clean & Focused */}
          <div id="opportunities-section" className="mb-8">

            {loading ? (
              <div className="flex justify-center items-center py-16">
                <Spin size="large" tip="Chargement des opportunités..." />
              </div>
            ) : error ? (
              <Alert
                message="Erreur"
                description={error}
                type="error"
                showIcon
                className="mb-6 rounded-xl shadow-md"
              />
            ) : (
              <>
                {/* Mobile Ad - Only visible on small screens */}
                <div className="mb-8 md:hidden">
                  <AdPlacement
                    adSlot="4567890123"
                    adSize="rectangle"
                    responsive={true}
                    fullWidth={true}
                  />
                </div>

                <div className="gy-pcard-wrap">
                  {opportunities.map((opportunity, index) => (
                    <EnhancedOpportunityCard
                      key={opportunity.id}
                      id={opportunity.id}
                      title={opportunity.title}
                      thumbnail={opportunity.thumbnail}
                      deadline={opportunity.deadline}
                      isActive={opportunity.isActive}
                      type={opportunity.type}
                      organization={opportunity.organization}
                      location={opportunity.location}
                      isRemote={opportunity.isRemote}
                      onClick={handleOpportunityClick}
                      index={index}
                      variant="greatyop"
                    />
                  ))}
                </div>

                {/* GreatYOP Pagination */}
                {(pagination.total > pagination.limit || opportunities.length > 0) && (
                  <GreatYOPPagination
                    current={pagination.page}
                    total={Math.max(pagination.total, opportunities.length)}
                    pageSize={pagination.limit}
                    onChange={handlePageChange}
                    showQuickJumper={false}
                  />
                )}

                {/* No Results Message */}
                {opportunities.length === 0 && (
                  <div className="text-center py-16 bg-gray-50 rounded-2xl shadow-sm border border-gray-100">
                    <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0V6a2 2 0 012 2v6M8 8v6a2 2 0 002 2h4a2 2 0 002-2V8M8 8V6a2 2 0 012-2h4a2 2 0 012 2v2" />
                    </svg>
                    <h3 className="mt-4 text-lg font-medium text-gray-900">Aucune opportunité trouvée</h3>
                    <p className="mt-2 text-sm text-gray-500 max-w-md mx-auto">
                      Essayez d'ajuster vos critères de recherche pour trouver ce que vous cherchez.
                    </p>
                  </div>
                )}
              </>
            )}
          </div>

          {/* Sidebar and Comments Section */}
          <div className="flex flex-col lg:flex-row gap-8">
            {/* Enhanced Sidebar - Wider for 2x4 layout */}
            <div className="lg:w-96">
              <UnifiedSidebar config={sidebarConfig} />

              {/* Desktop Ad - Only visible on large screens */}
              <div className="hidden lg:block mt-8">
                <AdPlacement
                  adSlot="9876543210"
                  adSize="rectangle"
                  responsive={false}
                />
              </div>
            </div>

            {/* Comments Section */}
            <div className="flex-1">
              <CommentsSection pageType="opportunity" pageId={config.type} />
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default StandardOpportunityPage;
